/**
 * ## Admin Chat Interface
 * Comprehensive chat management interface for administrators
 * Handles multiple customer conversations with advanced features
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  MessageSquare, 
  Send, 
  Search, 
  Users, 
  Clock,
  CheckCircle2,
  Circle,
  Package,
  User,
  Phone,
  Mail,
  MoreVertical,
  Pin,
  Archive
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage, ChatRoom } from '@/lib/types'
import { formatDate } from '@/lib/utils/dateUtils'

interface AdminChatInterfaceProps {
  userId: string
  userName?: string
  userEmail?: string
}

export function AdminChatInterface({ 
  userId, 
  userName, 
  userEmail 
}: AdminChatInterfaceProps) {
  const [selectedChatUserId, setSelectedChatUserId] = useState<string | null>(null)
  const [messageInput, setMessageInput] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Chat hook
  const {
    messages,
    chatRooms,
    isLoadingMessages,
    isLoadingRooms,
    sendMessage,
    markAsRead,
    typingUsers,
    unreadCount,
    error
  } = useChat({
    userId,
    userType: 'admin',
    selectedChatUserId: selectedChatUserId || undefined
  })

  /**
   * ## Auto-scroll to latest message
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  /**
   * ## Handle customer selection
   */
  const handleSelectCustomer = (customerId: string) => {
    setSelectedChatUserId(customerId)
    
    // Mark messages as read when opening chat
    const unreadMessages = messages
      .filter(msg => !msg.isRead && msg.senderType === 'customer' && msg.userId === customerId)
      .map(msg => msg.id)
    
    if (unreadMessages.length > 0) {
      markAsRead(unreadMessages)
    }
  }

  /**
   * ## Handle message sending
   */
  const handleSendMessage = async () => {
    if (!messageInput.trim() || !selectedChatUserId) return

    const message = messageInput.trim()
    setMessageInput('')
    
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  /**
   * ## Handle typing indicator
   */
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true)
      // ## TODO: Send typing indicator to customer
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
      // ## TODO: Send typing stopped
    }, 2000)
  }

  /**
   * ## Filter chat rooms based on search
   */
  const filteredChatRooms = chatRooms.filter(room =>
    room.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    room.customerEmail.toLowerCase().includes(searchQuery.toLowerCase())
  )

  /**
   * ## Get selected customer data
   */
  const selectedCustomer = chatRooms.find(room => room.userId === selectedChatUserId)

  /**
   * ## Customer List Item Component
   */
  const CustomerListItem = ({ room }: { room: ChatRoom }) => (
    <div
      onClick={() => handleSelectCustomer(room.userId)}
      className={`
        p-4 cursor-pointer transition-all duration-200 border-b border-slate-700/50
        hover:bg-slate-700/30
        ${selectedChatUserId === room.userId ? 'bg-slate-700/50 border-l-4 border-l-blue-500' : ''}
      `}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
              {room.customerName.charAt(0)}
            </div>
            <div className={`
              absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-slate-800
              ${room.isOnline ? 'bg-green-400' : 'bg-slate-500'}
            `} />
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-white truncate">{room.customerName}</h4>
            <p className="text-sm text-slate-400 truncate">{room.customerEmail}</p>
          </div>
        </div>
        
        <div className="flex flex-col items-end gap-1">
          {room.unreadCount > 0 && (
            <Badge className="bg-red-500 text-white text-xs px-2 py-1">
              {room.unreadCount}
            </Badge>
          )}
          <span className="text-xs text-slate-500">
            {formatDate(room.lastSeen, 'time')}
          </span>
        </div>
      </div>
      
      {room.lastMessage && (
        <p className="text-sm text-slate-400 truncate">
          {room.lastMessage.senderType === 'admin' ? 'أنت: ' : ''}
          {room.lastMessage.message}
        </p>
      )}
      
      {room.activeOrders.length > 0 && (
        <div className="flex items-center gap-1 mt-2">
          <Package className="h-3 w-3 text-orange-400" />
          <span className="text-xs text-orange-400">
            {room.activeOrders.length} طلب نشط
          </span>
        </div>
      )}
    </div>
  )

  /**
   * ## Message Component
   */
  const MessageBubble = ({ message }: { message: ChatMessage }) => {
    const isFromAdmin = message.senderType === 'admin'
    const isSystemMessage = message.messageType === 'system'

    if (isSystemMessage) {
      return (
        <div className="flex justify-center my-4">
          <div className="bg-slate-700/50 text-slate-300 text-sm px-3 py-1 rounded-full">
            {message.message}
          </div>
        </div>
      )
    }

    return (
      <div className={`flex ${isFromAdmin ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`
          max-w-[80%] px-4 py-3 rounded-2xl
          ${isFromAdmin 
            ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md' 
            : 'bg-slate-700/50 text-white rounded-bl-md'
          }
          animate-in slide-in-from-bottom-2 duration-300
        `}>
          <p className="text-sm leading-relaxed">{message.message}</p>
          
          <div className={`
            flex items-center gap-2 mt-2 text-xs
            ${isFromAdmin ? 'text-blue-100' : 'text-slate-400'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>
            
            {isFromAdmin && (
              <div className="flex items-center">
                {message.isRead ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-[700px] bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
      {/* Customer List Sidebar */}
      <div className="w-80 border-r border-slate-700/50 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 border-b border-slate-700/50">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center gap-2">
              <Users className="h-5 w-5" />
              المحادثات
            </h3>
            <Badge className="bg-blue-500 text-white">
              {unreadCount}
            </Badge>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="بحث في المحادثات..."
              className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 pl-10"
            />
          </div>
        </div>

        {/* Customer List */}
        <ScrollArea className="flex-1">
          {isLoadingRooms ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
            </div>
          ) : filteredChatRooms.length === 0 ? (
            <div className="text-center p-8">
              <MessageSquare className="h-12 w-12 text-slate-500 mx-auto mb-4" />
              <p className="text-slate-400">لا توجد محادثات</p>
            </div>
          ) : (
            filteredChatRooms.map((room) => (
              <CustomerListItem key={room.userId} room={room} />
            ))
          )}
        </ScrollArea>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedChatUserId ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-slate-700/50 bg-slate-800/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                    {selectedCustomer?.customerName.charAt(0)}
                  </div>
                  <div>
                    <h4 className="font-medium text-white">{selectedCustomer?.customerName}</h4>
                    <div className="flex items-center gap-4 text-sm text-slate-400">
                      <span className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {selectedCustomer?.customerEmail}
                      </span>
                      {selectedCustomer?.isOnline ? (
                        <span className="text-green-400">متصل الآن</span>
                      ) : (
                        <span>آخر ظهور {formatDate(selectedCustomer?.lastSeen || new Date(), 'relative')}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
              
              {typingUsers.length > 0 && (
                <div className="mt-2 text-sm text-blue-400 animate-pulse">
                  العميل يكتب الآن...
                </div>
              )}
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              {error && (
                <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-3 rounded-lg mb-4">
                  {error}
                </div>
              )}

              {isLoadingMessages ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
                </div>
              ) : messages.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <MessageSquare className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                    <p className="text-slate-400">ابدأ محادثة مع العميل</p>
                  </div>
                </div>
              ) : (
                <>
                  {messages.map((message) => (
                    <MessageBubble key={message.id} message={message} />
                  ))}
                  <div ref={messagesEndRef} />
                </>
              )}
            </ScrollArea>

            {/* Input Area */}
            <div className="border-t border-slate-700/50 p-4">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={messageInput}
                  onChange={(e) => {
                    setMessageInput(e.target.value)
                    handleTyping()
                  }}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      handleSendMessage()
                    }
                  }}
                  placeholder="اكتب رسالتك للعميل..."
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                  maxLength={1000}
                />
                
                <Button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim()}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-6"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          /* No Chat Selected */
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquare className="h-16 w-16 text-slate-500 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-white mb-2">اختر محادثة</h3>
              <p className="text-slate-400">
                اختر عميل من القائمة لبدء أو متابعة المحادثة
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Customer Info Sidebar (Optional) */}
      {selectedCustomer && (
        <div className="w-64 border-l border-slate-700/50 bg-slate-800/30 p-4">
          <h4 className="font-medium text-white mb-4">معلومات العميل</h4>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm text-slate-400">الاسم</label>
              <p className="text-white">{selectedCustomer.customerName}</p>
            </div>
            
            <div>
              <label className="text-sm text-slate-400">البريد الإلكتروني</label>
              <p className="text-white text-sm">{selectedCustomer.customerEmail}</p>
            </div>
            
            <div>
              <label className="text-sm text-slate-400">الطلبات النشطة</label>
              <p className="text-orange-400">{selectedCustomer.activeOrders.length} طلب</p>
            </div>
            
            <div>
              <label className="text-sm text-slate-400">تاريخ التسجيل</label>
              <p className="text-white text-sm">{formatDate(selectedCustomer.createdAt, 'date')}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
