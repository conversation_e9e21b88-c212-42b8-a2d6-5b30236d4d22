/**
 * ## Admin Chat Interface
 * Comprehensive chat management interface for administrators
 * Handles multiple customer conversations with advanced features
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  MessageSquare,
  Send,
  Search,
  Users,
  Clock,
  CheckCircle2,
  Circle,
  Package,
  User,
  Phone,
  Mail,
  MoreVertical,
  Pin,
  Archive,
  X,
  Shield
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage, ChatRoom } from '@/lib/types'
import { formatDate } from '@/lib/utils/dateUtils'

interface AdminChatInterfaceProps {
  userId: string
  userName?: string
  userEmail?: string
}

export function AdminChatInterface({ 
  userId, 
  userName, 
  userEmail 
}: AdminChatInterfaceProps) {
  const [selectedChatUserId, setSelectedChatUserId] = useState<string | null>(null)
  const [messageInput, setMessageInput] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [showCustomerDetails, setShowCustomerDetails] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Chat hook
  const {
    messages,
    chatRooms,
    isLoadingMessages,
    isLoadingRooms,
    sendMessage,
    markAsRead,
    typingUsers,
    unreadCount,
    error
  } = useChat({
    userId,
    userType: 'admin',
    selectedChatUserId: selectedChatUserId || undefined
  })

  /**
   * ## Auto-scroll to latest message
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  /**
   * ## Handle customer selection
   */
  const handleSelectCustomer = (customerId: string) => {
    setSelectedChatUserId(customerId)
    
    // Mark messages as read when opening chat
    const unreadMessages = messages
      .filter(msg => !msg.isRead && msg.senderType === 'customer' && msg.userId === customerId)
      .map(msg => msg.id)
    
    if (unreadMessages.length > 0) {
      markAsRead(unreadMessages)
    }
  }

  /**
   * ## Handle message sending
   */
  const handleSendMessage = async () => {
    if (!messageInput.trim() || !selectedChatUserId) return

    const message = messageInput.trim()
    setMessageInput('')
    
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  /**
   * ## Handle typing indicator
   */
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true)
      // ## TODO: Send typing indicator to customer
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
      // ## TODO: Send typing stopped
    }, 2000)
  }

  /**
   * ## Filter chat rooms based on search
   */
  const filteredChatRooms = chatRooms.filter(room =>
    room.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    room.customerEmail.toLowerCase().includes(searchQuery.toLowerCase())
  )

  /**
   * ## Get selected customer data
   */
  const selectedCustomer = chatRooms.find(room => room.userId === selectedChatUserId)

  /**
   * ## Customer List Item Component - WhatsApp Style
   */
  const CustomerListItem = ({ room }: { room: ChatRoom }) => (
    <div
      onClick={() => handleSelectCustomer(room.userId)}
      className={`
        p-4 cursor-pointer transition-all duration-200
        hover:bg-slate-700/20 active:bg-slate-700/40
        ${selectedChatUserId === room.userId ? 'bg-slate-700/30' : ''}
      `}
    >
      <div className="flex items-center gap-3">
        {/* Avatar - Larger WhatsApp Style */}
        <div className="relative flex-shrink-0">
          <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
            {room.customerName.charAt(0)}
          </div>
          {room.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-800" />
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="font-semibold text-white truncate text-base">
              {room.customerName}
            </h4>
            <div className="flex items-center gap-2 flex-shrink-0">
              {room.unreadCount > 0 && (
                <div className="bg-green-500 text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1.5">
                  {room.unreadCount > 99 ? '99+' : room.unreadCount}
                </div>
              )}
              <span className="text-xs text-slate-400">
                {formatDate(room.lastSeen, 'time')}
              </span>
            </div>
          </div>

          {/* Last Message */}
          {room.lastMessage ? (
            <p className="text-sm text-slate-400 truncate leading-relaxed">
              {room.lastMessage.senderType === 'admin' && (
                <span className="text-blue-400">أنت: </span>
              )}
              {room.lastMessage.message}
            </p>
          ) : (
            <p className="text-sm text-slate-500 italic">لا توجد رسائل</p>
          )}

          {/* Active Orders Indicator */}
          {room.activeOrders.length > 0 && (
            <div className="flex items-center gap-1 mt-2">
              <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" />
              <span className="text-xs text-orange-400 font-medium">
                {room.activeOrders.length} طلب نشط
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  /**
   * ## Message Component - WhatsApp Style
   */
  const MessageBubble = ({ message }: { message: ChatMessage }) => {
    const isFromAdmin = message.senderType === 'admin'
    const isSystemMessage = message.messageType === 'system'

    if (isSystemMessage) {
      return (
        <div className="flex justify-center my-6">
          <div className="bg-slate-700/30 text-slate-300 text-sm px-4 py-2 rounded-full border border-slate-600/30">
            {message.message}
          </div>
        </div>
      )
    }

    return (
      <div className={`flex ${isFromAdmin ? 'justify-end' : 'justify-start'} mb-3`}>
        <div className={`
          max-w-[85%] md:max-w-[70%] px-4 py-3 rounded-2xl shadow-lg
          ${isFromAdmin
            ? 'bg-green-500 text-white rounded-br-md'
            : 'bg-white text-slate-900 rounded-bl-md'
          }
          animate-in slide-in-from-bottom-2 duration-200
        `}>
          <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
            {message.message}
          </p>

          <div className={`
            flex items-center justify-end gap-1 mt-2 text-xs
            ${isFromAdmin ? 'text-green-100' : 'text-slate-500'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>

            {isFromAdmin && (
              <div className="flex items-center ml-1">
                {message.isRead ? (
                  <svg className="w-4 h-4 text-blue-200" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4 text-green-200" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-[600px] md:h-[700px] bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
      {/* Mobile: Show either chat list or chat window */}
      <div className="flex h-full">
        {/* Customer List - Full width on mobile when no chat selected */}
        <div className={`${
          selectedChatUserId ? 'hidden md:flex md:w-80' : 'flex w-full md:w-80'
        } border-r border-slate-700/50 flex-col`}>

          {/* Header - WhatsApp Style */}
          <div className="p-4 bg-slate-800/80 border-b border-slate-700/50">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-xl font-bold text-white">المحادثات</h3>
              {unreadCount > 0 && (
                <Badge className="bg-green-500 text-white px-2 py-1 text-sm">
                  {unreadCount}
                </Badge>
              )}
            </div>

            {/* Search - WhatsApp Style */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="ابحث عن محادثة..."
                className="bg-slate-700/30 border-slate-600/50 text-white placeholder:text-slate-400 pl-10 rounded-full"
              />
            </div>
          </div>

          {/* Customer List - WhatsApp Style */}
          <ScrollArea className="flex-1">
            {isLoadingRooms ? (
              <div className="flex items-center justify-center p-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
              </div>
            ) : filteredChatRooms.length === 0 ? (
              <div className="text-center p-12">
                <MessageSquare className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">لا توجد محادثات</p>
                <p className="text-slate-500 text-sm mt-2">ستظهر المحادثات هنا عند بدء العملاء بالتواصل</p>
              </div>
            ) : (
              <div className="divide-y divide-slate-700/30">
                {filteredChatRooms.map((room) => (
                  <CustomerListItem key={room.userId} room={room} />
                ))}
              </div>
            )}
          </ScrollArea>
        </div>

        {/* Chat Area - Full width on mobile when chat selected */}
        <div className={`${
          selectedChatUserId ? 'flex w-full' : 'hidden md:flex md:flex-1'
        } flex-col`}>
          {selectedChatUserId ? (
            <>
              {/* Chat Header - WhatsApp Style */}
              <div className="p-4 bg-slate-800/80 border-b border-slate-700/50">
                <div className="flex items-center gap-3">
                  {/* Back Button for Mobile */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedChatUserId(null)}
                    className="md:hidden p-2 hover:bg-slate-700/50"
                  >
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </Button>

                  {/* Customer Info */}
                  <div className="flex items-center gap-3 flex-1">
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold">
                        {selectedCustomer?.customerName.charAt(0)}
                      </div>
                      {selectedCustomer?.isOnline && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 rounded-full border-2 border-slate-800" />
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-white text-lg truncate">
                        {selectedCustomer?.customerName}
                      </h4>
                      <p className="text-sm text-slate-400">
                        {selectedCustomer?.isOnline ? (
                          <span className="text-green-400 flex items-center gap-1">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                            متصل الآن
                          </span>
                        ) : (
                          `آخر ظهور ${formatDate(selectedCustomer?.lastSeen || new Date(), 'relative')}`
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-2 hover:bg-slate-700/50"
                      onClick={() => setShowCustomerDetails(true)}
                    >
                      <User className="h-5 w-5 text-slate-400" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-2 hover:bg-slate-700/50"
                    >
                      <MoreVertical className="h-5 w-5 text-slate-400" />
                    </Button>
                  </div>
                </div>

                {/* Typing Indicator */}
                {typingUsers.length > 0 && (
                  <div className="mt-3 text-sm text-green-400 animate-pulse flex items-center gap-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                    </div>
                    العميل يكتب...
                  </div>
                )}
              </div>

              {/* Messages - WhatsApp Style */}
              <ScrollArea className="flex-1 p-4 bg-slate-900/20">
                {error && (
                  <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-3 rounded-lg mb-4">
                    {error}
                  </div>
                )}

                {isLoadingMessages ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
                  </div>
                ) : messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <MessageSquare className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                      <p className="text-slate-400 text-lg">ابدأ محادثة مع العميل</p>
                      <p className="text-slate-500 text-sm mt-2">اكتب رسالة لبدء المحادثة</p>
                    </div>
                  </div>
                ) : (
                  <>
                    {messages.map((message) => (
                      <MessageBubble key={message.id} message={message} />
                    ))}
                    <div ref={messagesEndRef} />
                  </>
                )}
              </ScrollArea>

              {/* Input Area - WhatsApp Style */}
              <div className="border-t border-slate-700/50 p-4 bg-slate-800/50">
                <div className="flex items-end gap-3">
                  {/* Attachment Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-2 hover:bg-slate-700/50 text-slate-400 hover:text-white"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                    </svg>
                  </Button>

                  {/* Message Input */}
                  <div className="flex-1 relative">
                    <Input
                      ref={inputRef}
                      value={messageInput}
                      onChange={(e) => {
                        setMessageInput(e.target.value)
                        handleTyping()
                      }}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault()
                          handleSendMessage()
                        }
                      }}
                      placeholder="اكتب رسالة..."
                      className="bg-slate-700/30 border-slate-600/50 text-white placeholder:text-slate-400 rounded-full pr-4 pl-12 py-3 focus:ring-2 focus:ring-green-500/50"
                      maxLength={1000}
                    />

                    {/* Emoji Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-slate-600/50 text-slate-400 hover:text-white"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </Button>
                  </div>

                  {/* Send Button */}
                  <Button
                    onClick={handleSendMessage}
                    disabled={!messageInput.trim()}
                    className="bg-green-500 hover:bg-green-600 text-white rounded-full p-3 h-12 w-12 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Send className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            /* No Chat Selected - Desktop Only */}
            <div className="hidden md:flex flex-1 items-center justify-center">
              <div className="text-center">
                <MessageSquare className="h-20 w-20 text-slate-500 mx-auto mb-6" />
                <h3 className="text-2xl font-medium text-white mb-3">اختر محادثة</h3>
                <p className="text-slate-400 text-lg">
                  اختر عميل من القائمة لبدء أو متابعة المحادثة
                </p>
                <p className="text-slate-500 text-sm mt-2">
                  ستظهر المحادثة هنا عند اختيار عميل
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Customer Details Modal - WhatsApp Style */}
      {showCustomerDetails && selectedCustomer && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-slate-800 rounded-2xl w-full max-w-md max-h-[80vh] overflow-hidden">
            {/* Modal Header */}
            <div className="p-6 border-b border-slate-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-white">معلومات العميل</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCustomerDetails(false)}
                  className="p-2 hover:bg-slate-700/50"
                >
                  <X className="h-5 w-5 text-slate-400" />
                </Button>
              </div>
            </div>

            {/* Customer Info */}
            <div className="p-6">
              {/* Avatar and Name */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg">
                  {selectedCustomer.customerName.charAt(0)}
                </div>
                <h4 className="text-xl font-bold text-white mb-1">
                  {selectedCustomer.customerName}
                </h4>
                <p className="text-slate-400">{selectedCustomer.customerEmail}</p>
              </div>

              {/* Details */}
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <span className="text-slate-400">الحالة</span>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${selectedCustomer.isOnline ? 'bg-green-400' : 'bg-slate-500'}`} />
                    <span className="text-white">
                      {selectedCustomer.isOnline ? 'متصل الآن' : 'غير متصل'}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <span className="text-slate-400">آخر ظهور</span>
                  <span className="text-white">
                    {formatDate(selectedCustomer.lastSeen, 'relative')}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <span className="text-slate-400">الطلبات النشطة</span>
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-orange-400" />
                    <span className="text-orange-400 font-medium">
                      {selectedCustomer.activeOrders.length} طلب
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <span className="text-slate-400">تاريخ التسجيل</span>
                  <span className="text-white">
                    {formatDate(selectedCustomer.createdAt, 'date')}
                  </span>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-6 space-y-3">
                <Button
                  className="w-full bg-green-500 hover:bg-green-600 text-white"
                  onClick={() => {
                    // View customer orders
                    setShowCustomerDetails(false)
                  }}
                >
                  <Package className="h-4 w-4 mr-2" />
                  عرض الطلبات
                </Button>

                <Button
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
                  onClick={() => {
                    // Block/unblock customer
                    setShowCustomerDetails(false)
                  }}
                >
                  <Shield className="h-4 w-4 mr-2" />
                  إدارة العميل
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
