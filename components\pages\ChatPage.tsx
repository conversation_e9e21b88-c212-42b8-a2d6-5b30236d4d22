/**
 * ## Chat Page Component
 * Main page for customer and admin chat interface
 * Handles role detection and renders appropriate chat interface
 */

'use client'

import { useState, useEffect } from 'react'
import { ChatSystem, QuickChatButton } from '@/components/chat/ChatSystem'
import { ChatNotifications } from '@/components/chat/ChatNotifications'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  MessageCircle, 
  Settings, 
  Users, 
  Clock,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react'

interface ChatPageProps {
  // ## Supabase Integration: These will come from auth context
  userRole?: 'customer' | 'admin'
  userId?: string
  userName?: string
  userEmail?: string
}

export function ChatPage({ 
  userRole = 'customer', 
  userId = 'demo-user',
  userName = 'مستخدم تجريبي',
  userEmail = '<EMAIL>'
}: ChatPageProps) {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)
  const [showQuickAccess, setShowQuickAccess] = useState(false)

  /**
   * ## Security: Validate User Access
   */
  useEffect(() => {
    // ## TODO: Replace with Supabase auth check
    // const validateAccess = async () => {
    //   const { data: { user }, error } = await supabase.auth.getUser()
    //   if (error || !user) {
    //     router.push('/login')
    //     return
    //   }
    //   
    //   // Check user role and permissions
    //   const { data: profile } = await supabase
    //     .from('profiles')
    //     .select('role, permissions')
    //     .eq('id', user.id)
    //     .single()
    //   
    //   if (!profile || !['customer', 'admin'].includes(profile.role)) {
    //     router.push('/unauthorized')
    //   }
    // }
    // validateAccess()
  }, [])

  /**
   * ## Chat Notifications Setup
   */
  const notifications = ChatNotifications({
    userId,
    userType: userRole,
    isEnabled: notificationsEnabled,
    onToggle: setNotificationsEnabled
  })

  /**
   * ## Page Header Component
   */
  const PageHeader = () => (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-2xl font-bold text-white flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
              <MessageCircle className="h-6 w-6 text-white" />
            </div>
            {userRole === 'admin' ? 'إدارة المحادثات' : 'الدعم الفني'}
          </h1>
          <p className="text-slate-400 mt-1">
            {userRole === 'admin' 
              ? 'إدارة محادثات العملاء والرد على استفساراتهم'
              : 'تواصل مع فريق الدعم الفني للحصول على المساعدة'
            }
          </p>
        </div>

        <div className="flex items-center gap-2">
          {notifications.QuickToggleButton()}
          
          {userRole === 'admin' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowQuickAccess(!showQuickAccess)}
              className="border-slate-600 text-slate-300 hover:text-white"
            >
              <Settings className="h-4 w-4 mr-2" />
              الإعدادات
            </Button>
          )}
        </div>
      </div>

      {/* Status Indicators */}
      <div className="flex items-center gap-4 text-sm">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          <span className="text-slate-300">متصل</span>
        </div>
        
        {userRole === 'admin' && (
          <>
            <div className="flex items-center gap-2 text-blue-400">
              <Users className="h-4 w-4" />
              <span>5 عملاء نشطين</span>
            </div>
            <div className="flex items-center gap-2 text-orange-400">
              <Clock className="h-4 w-4" />
              <span>متوسط الرد: 2 دقيقة</span>
            </div>
          </>
        )}
      </div>
    </div>
  )

  /**
   * ## Admin Quick Stats (Admin Only)
   */
  const AdminQuickStats = () => {
    if (userRole !== 'admin') return null

    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">المحادثات النشطة</p>
                <p className="text-2xl font-bold text-white">12</p>
              </div>
              <MessageCircle className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">رسائل غير مقروءة</p>
                <p className="text-2xl font-bold text-red-400">8</p>
              </div>
              <Badge className="bg-red-500 text-white">جديد</Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">متوسط وقت الرد</p>
                <p className="text-2xl font-bold text-green-400">2.5م</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">معدل الرضا</p>
                <p className="text-2xl font-bold text-yellow-400">98%</p>
              </div>
              <Shield className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  /**
   * ## Customer Help Section
   */
  const CustomerHelpSection = () => {
    if (userRole !== 'customer') return null

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card className="bg-gradient-to-r from-blue-500/10 to-blue-600/10 border-blue-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Zap className="h-5 w-5 text-blue-400" />
              <h3 className="font-medium text-white">استجابة سريعة</h3>
            </div>
            <p className="text-sm text-slate-300">
              نرد على رسائلك خلال دقائق معدودة
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500/10 to-green-600/10 border-green-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Shield className="h-5 w-5 text-green-400" />
              <h3 className="font-medium text-white">دعم متخصص</h3>
            </div>
            <p className="text-sm text-slate-300">
              فريق متخصص في خدمات الألعاب
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500/10 to-purple-600/10 border-purple-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Clock className="h-5 w-5 text-purple-400" />
              <h3 className="font-medium text-white">متاح 24/7</h3>
            </div>
            <p className="text-sm text-slate-300">
              خدمة العملاء متاحة على مدار الساعة
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-4">
      <div className="max-w-7xl mx-auto">
        <PageHeader />
        <AdminQuickStats />
        <CustomerHelpSection />

        {/* Main Chat Interface */}
        <div className="relative">
          <ChatSystem
            userRole={userRole}
            userId={userId}
            userName={userName}
            userEmail={userEmail}
            className="mb-6"
          />

          {/* Notification Settings Panel (Collapsible) */}
          {showQuickAccess && userRole === 'admin' && (
            <div className="mt-6">
              {notifications.NotificationSettingsPanel()}
            </div>
          )}
        </div>

        {/* Quick Access Button for Mobile */}
        <QuickChatButton
          userRole={userRole}
          unreadCount={userRole === 'admin' ? 8 : 0}
          onClick={() => {
            // Scroll to chat or open modal
            document.querySelector('.chat-system')?.scrollIntoView({ 
              behavior: 'smooth' 
            })
          }}
        />
      </div>
    </div>
  )
}

/**
 * ## Chat Page Wrapper with Role Detection
 * Automatically detects user role and renders appropriate interface
 */
export function ChatPageWrapper() {
  const [userRole, setUserRole] = useState<'customer' | 'admin'>('customer')
  const [userId, setUserId] = useState('demo-user')
  const [userName, setUserName] = useState('مستخدم تجريبي')
  const [userEmail, setUserEmail] = useState('<EMAIL>')

  useEffect(() => {
    // ## TODO: Replace with Supabase auth
    // const getUserData = async () => {
    //   const { data: { user } } = await supabase.auth.getUser()
    //   if (user) {
    //     const { data: profile } = await supabase
    //       .from('profiles')
    //       .select('role, full_name')
    //       .eq('id', user.id)
    //       .single()
    //     
    //     setUserRole(profile?.role || 'customer')
    //     setUserId(user.id)
    //     setUserName(profile?.full_name || user.email)
    //     setUserEmail(user.email)
    //   }
    // }
    // getUserData()

    // Demo role switcher for development
    const urlParams = new URLSearchParams(window.location.search)
    const role = urlParams.get('role') as 'customer' | 'admin'
    if (role && ['customer', 'admin'].includes(role)) {
      setUserRole(role)
      setUserName(role === 'admin' ? 'مدير النظام' : 'عميل تجريبي')
    }
  }, [])

  return (
    <ChatPage
      userRole={userRole}
      userId={userId}
      userName={userName}
      userEmail={userEmail}
    />
  )
}
