# 💬 Real-Time Chat System Documentation

## Overview

A comprehensive real-time chat system designed for game charging services, enabling seamless communication between customers and administrators during order processing, especially for OTP verification workflows.

## 🎯 Key Features

### **Customer Features**
- ✅ Simple, intuitive chat interface
- ✅ Real-time messaging with admins
- ✅ Order context integration
- ✅ Typing indicators
- ✅ Message read receipts
- ✅ Browser notifications
- ✅ Mobile-responsive design
- ✅ Arabic RTL support

### **Admin Features**
- ✅ Multi-customer chat management
- ✅ Customer list with unread counts
- ✅ Order information sidebar
- ✅ Quick customer search
- ✅ Online/offline status tracking
- ✅ Chat analytics dashboard
- ✅ Notification settings
- ✅ Professional admin interface

### **Technical Features**
- ✅ Real-time communication (Supabase ready)
- ✅ Message validation & sanitization
- ✅ Rate limiting protection
- ✅ File upload support (images)
- ✅ Typing indicators
- ✅ Connection status monitoring
- ✅ Offline message queuing
- ✅ Security & role-based access

## 🏗️ Architecture

### **Component Structure**
```
components/chat/
├── ChatSystem.tsx              # Main dynamic component
├── CustomerChatInterface.tsx   # Customer-facing interface
├── AdminChatInterface.tsx      # Admin management interface
├── ChatNotifications.tsx       # Notification system
└── ChatUtils.ts               # Utility functions

lib/hooks/
└── useChat.ts                 # Main chat hook

lib/types/
└── index.ts                   # Chat type definitions
```

### **Database Schema (Supabase Ready)**

```sql
-- Main chats table
CREATE TABLE chats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,                    -- Customer ID
  admin_id UUID,                           -- Admin who responded
  message TEXT NOT NULL,
  sender_type VARCHAR(10) NOT NULL,        -- 'customer' or 'admin'
  order_id VARCHAR(50),                    -- Optional: link to order
  message_type VARCHAR(20) DEFAULT 'text', -- 'text', 'image', 'system'
  attachment_url TEXT,                     -- For file sharing
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_chats_user_created ON chats(user_id, created_at DESC);
CREATE INDEX idx_chats_unread ON chats(is_read, sender_type, created_at DESC) 
WHERE is_read = false;
CREATE INDEX idx_chats_order ON chats(order_id) WHERE order_id IS NOT NULL;

-- User presence tracking
CREATE TABLE user_presence (
  user_id UUID PRIMARY KEY,
  user_type VARCHAR(10) NOT NULL,
  is_online BOOLEAN DEFAULT false,
  last_seen TIMESTAMP DEFAULT NOW(),
  current_page VARCHAR(255)
);
```

## 🚀 Usage Examples

### **Customer Interface**
```tsx
import { ChatSystem } from '@/components/chat/ChatSystem'

function CustomerPage() {
  return (
    <ChatSystem
      userRole="customer"
      userId="customer-123"
      userName="أحمد محمد"
      userEmail="<EMAIL>"
    />
  )
}
```

### **Admin Interface**
```tsx
import { ChatSystem } from '@/components/chat/ChatSystem'

function AdminPage() {
  return (
    <ChatSystem
      userRole="admin"
      userId="admin-456"
      userName="مدير النظام"
      userEmail="<EMAIL>"
    />
  )
}
```

### **Navigation Integration**
```tsx
import { ChatBadge } from '@/components/chat/ChatSystem'

// In navigation component
<Button>
  <MessageCircle className="h-5 w-5" />
  المحادثات
  {unreadCount > 0 && (
    <ChatBadge count={unreadCount} />
  )}
</Button>
```

## 🔧 Integration Steps

### **1. Supabase Setup**
```sql
-- Run the database schema above
-- Enable Row Level Security (RLS)
-- Set up real-time subscriptions
```

### **2. Environment Variables**
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### **3. Real-time Configuration**
```typescript
// In useChat.ts, replace mock data with:
const { data, error } = await supabase
  .from('chats')
  .select('*')
  .eq('user_id', userId)
  .order('created_at', { ascending: true })

// Setup real-time subscription
const subscription = supabase
  .channel('chat')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'chats',
    filter: `user_id=eq.${userId}`
  }, handleNewMessage)
  .subscribe()
```

## 🎮 Game Charging Workflow

### **Typical OTP Verification Flow**
1. **Customer** places order for game charging
2. **Admin** receives order notification
3. **Chat** automatically opens for order
4. **Admin**: "سأبدأ بتسجيل الدخول لحسابك، انتظر رمز التحقق"
5. **Customer**: "حسناً، أنا جاهز"
6. **Admin**: "تم إرسال رمز التحقق لبريدك، أرسل لي الرمز"
7. **Customer**: "الرمز هو 123456"
8. **Admin**: "ممتاز! جاري معالجة الشحن..."
9. **Admin**: "تم! تحقق من حسابك"
10. **Customer**: "شكراً لك!"

## 🔒 Security Features

### **Message Validation**
- Content sanitization (HTML/XSS protection)
- Length limits (1000 characters)
- Spam detection patterns
- Rate limiting (10 messages/minute)

### **Access Control**
- Role-based permissions
- User authentication required
- Admin-only features protected
- Customer data isolation

### **File Upload Security**
- File type validation (images only)
- Size limits (5MB max)
- Virus scanning ready
- Secure URL generation

## 📱 Mobile Experience

### **Responsive Design**
- Touch-optimized interface
- Swipe gestures support
- Mobile keyboard handling
- Offline message queuing

### **Progressive Web App Ready**
- Push notifications
- Background sync
- Offline functionality
- App-like experience

## 🎨 Customization

### **Theming**
```css
/* Custom chat bubble colors */
.chat-bubble-customer {
  @apply bg-gradient-to-r from-blue-500 to-blue-600;
}

.chat-bubble-admin {
  @apply bg-slate-700/50;
}
```

### **Animations**
- Smooth message animations
- Typing indicators
- Connection status transitions
- Notification effects

## 📊 Analytics & Monitoring

### **Chat Metrics**
- Response time tracking
- Message volume statistics
- Customer satisfaction scores
- Admin performance metrics

### **System Health**
- Connection status monitoring
- Error rate tracking
- Performance metrics
- Real-time user counts

## 🚀 Deployment Checklist

- [ ] Database schema deployed
- [ ] Real-time subscriptions enabled
- [ ] Environment variables configured
- [ ] File upload storage configured
- [ ] Push notification setup
- [ ] Error monitoring enabled
- [ ] Performance monitoring setup
- [ ] Security audit completed

## 🔮 Future Enhancements

### **Phase 2 Features**
- Voice messages
- Video calls
- Screen sharing
- Chatbot integration
- Multi-language support

### **Phase 3 Features**
- AI-powered responses
- Advanced analytics
- Integration APIs
- Mobile apps
- Desktop notifications

## 🆘 Troubleshooting

### **Common Issues**
1. **Messages not appearing**: Check Supabase connection
2. **Notifications not working**: Verify browser permissions
3. **Slow performance**: Check database indexes
4. **Connection drops**: Implement reconnection logic

### **Debug Mode**
```typescript
// Enable debug logging
localStorage.setItem('chat_debug', 'true')
```

## 📞 Support

For technical support or questions about the chat system:
- Check the component documentation
- Review Supabase logs
- Test with demo data first
- Verify user permissions

---

**Built with ❤️ for seamless customer communication**
